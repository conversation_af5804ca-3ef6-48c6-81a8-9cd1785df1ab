"""
Logging Factories Module
Factory classes for creating logging components based on configuration
"""
import logging
import sys
from typing import Dict, Any, Optional, Type
from abc import ABC, abstractmethod

from .config import FormatterConfig, HandlerConfig, FilterConfig, LoggerConfig
from .formatters import (
    ERP<PERSON>ormatter, JSONFormatter, ColoredFormatter,
    PerformanceFormatter, DatabaseFormatter, SecurityFormatter
)
from .handlers import (
    SafeConsoleHandler, RotatingFileHandler, TimedRotatingFileHandler, DatabaseHandler
)
from .filters import (
    LevelFilter, ModuleFilter, PerformanceFilter, DuplicateFilter, RateLimitFilter
)


class ComponentFactory(ABC):
    """Abstract base class for component factories"""
    
    @abstractmethod
    def create(self, name: str, config: Any) -> Any:
        """Create a component based on configuration"""
        pass
    
    @abstractmethod
    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of type names to classes"""
        pass


class FormatterFactory(ComponentFactory):
    """Factory for creating formatters"""
    
    _FORMATTER_TYPES = {
        'erp': ERPFormatter,
        'json': JSONFormatter,
        'colored': ColoredFormatter,
        'performance': PerformanceFormatter,
        'database': DatabaseFormatter,
        'security': SecurityFormatter,
    }
    
    def create(self, name: str, config: FormatterConfig) -> logging.Formatter:
        """Create a formatter based on configuration"""
        formatter_class = self._FORMATTER_TYPES.get(config.type)
        if not formatter_class:
            raise ValueError(f"Unknown formatter type: {config.type}")
        
        # Build kwargs based on formatter type and config
        kwargs = {}
        
        if config.type == 'erp':
            kwargs['include_context'] = config.include_context
            if config.use_unicode is not None:
                kwargs['use_unicode'] = config.use_unicode
        
        elif config.type == 'json':
            kwargs['include_extra'] = config.include_extra
        
        elif config.type == 'colored':
            if config.use_colors is not None:
                kwargs['use_colors'] = config.use_colors
            if config.use_unicode is not None:
                kwargs['use_unicode'] = config.use_unicode
        
        return formatter_class(**kwargs)
    
    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of formatter type names to classes"""
        return self._FORMATTER_TYPES.copy()


class FilterFactory(ComponentFactory):
    """Factory for creating filters"""
    
    _FILTER_TYPES = {
        'level': LevelFilter,
        'module': ModuleFilter,
        'performance': PerformanceFilter,
        'duplicate': DuplicateFilter,
        'rate_limit': RateLimitFilter,
    }
    
    def create(self, name: str, config: FilterConfig) -> logging.Filter:
        """Create a filter based on configuration"""
        filter_class = self._FILTER_TYPES.get(config.type)
        if not filter_class:
            raise ValueError(f"Unknown filter type: {config.type}")
        
        # Build kwargs based on filter type and config
        kwargs = {}
        
        if config.type == 'level':
            if config.min_level:
                kwargs['min_level'] = config.min_level
            if config.max_level:
                kwargs['max_level'] = config.max_level
        
        elif config.type == 'module':
            if config.include_patterns:
                kwargs['include_patterns'] = config.include_patterns
            if config.exclude_patterns:
                kwargs['exclude_patterns'] = config.exclude_patterns
        
        elif config.type == 'performance':
            if config.min_duration is not None:
                kwargs['min_duration'] = config.min_duration
            if config.max_duration is not None:
                kwargs['max_duration'] = config.max_duration
        
        elif config.type == 'duplicate':
            kwargs['max_duplicates'] = config.max_duplicates
            kwargs['time_window'] = config.time_window
        
        elif config.type == 'rate_limit':
            # Rate limit filter would need additional configuration
            pass
        
        return filter_class(**kwargs)
    
    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of filter type names to classes"""
        return self._FILTER_TYPES.copy()


class HandlerFactory(ComponentFactory):
    """Factory for creating handlers"""
    
    _HANDLER_TYPES = {
        'console': SafeConsoleHandler,
        'file': logging.FileHandler,
        'rotating_file': RotatingFileHandler,
        'timed_rotating_file': TimedRotatingFileHandler,
        'database': DatabaseHandler,
    }
    
    def __init__(self, formatter_factory: FormatterFactory, filter_factory: FilterFactory):
        self.formatter_factory = formatter_factory
        self.filter_factory = filter_factory
    
    def create(self, name: str, config: HandlerConfig, 
               formatters: Dict[str, logging.Formatter],
               filters: Dict[str, logging.Filter]) -> logging.Handler:
        """Create a handler based on configuration"""
        handler_class = self._HANDLER_TYPES.get(config.type)
        if not handler_class:
            raise ValueError(f"Unknown handler type: {config.type}")
        
        # Build kwargs based on handler type and config
        kwargs = {}
        
        if config.type == 'console':
            if config.stream == 'stderr':
                kwargs['stream'] = sys.stderr
            else:
                kwargs['stream'] = sys.stdout
        
        elif config.type == 'file':
            if not config.filename:
                raise ValueError(f"Handler '{name}' of type 'file' requires filename")
            kwargs['filename'] = config.filename
            kwargs['encoding'] = 'utf-8'
        
        elif config.type == 'rotating_file':
            if not config.filename:
                raise ValueError(f"Handler '{name}' of type 'rotating_file' requires filename")
            kwargs['filename'] = config.filename
            kwargs['maxBytes'] = config.max_bytes
            kwargs['backupCount'] = config.backup_count
            kwargs['encoding'] = 'utf-8'
        
        elif config.type == 'timed_rotating_file':
            if not config.filename:
                raise ValueError(f"Handler '{name}' of type 'timed_rotating_file' requires filename")
            kwargs['filename'] = config.filename
            kwargs['when'] = config.when
            kwargs['interval'] = config.interval
            kwargs['backupCount'] = config.backup_count
            kwargs['encoding'] = 'utf-8'
        
        elif config.type == 'database':
            kwargs['table_name'] = config.table_name
            kwargs['max_buffer_size'] = config.max_buffer_size
            kwargs['flush_interval'] = config.flush_interval
        
        # Create the handler
        handler = handler_class(**kwargs)
        
        # Set level
        handler.setLevel(getattr(logging, config.level.upper()))
        
        # Set formatter
        if config.formatter in formatters:
            handler.setFormatter(formatters[config.formatter])
        else:
            raise ValueError(f"Handler '{name}' references unknown formatter '{config.formatter}'")
        
        # Add filters
        for filter_name in config.filters:
            if filter_name in filters:
                handler.addFilter(filters[filter_name])
            else:
                raise ValueError(f"Handler '{name}' references unknown filter '{filter_name}'")
        
        return handler
    
    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of handler type names to classes"""
        return self._HANDLER_TYPES.copy()


class LoggerFactory(ComponentFactory):
    """Factory for creating loggers"""
    
    def create(self, name: str, config: LoggerConfig,
               handlers: Dict[str, logging.Handler],
               filters: Dict[str, logging.Filter]) -> logging.Logger:
        """Create a logger based on configuration"""
        logger = logging.getLogger(name)
        
        # Set level
        logger.setLevel(getattr(logging, config.level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Add handlers
        for handler_name in config.handlers:
            if handler_name in handlers:
                logger.addHandler(handlers[handler_name])
            else:
                raise ValueError(f"Logger '{name}' references unknown handler '{handler_name}'")
        
        # Add filters
        for filter_name in config.filters:
            if filter_name in filters:
                logger.addFilter(filters[filter_name])
            else:
                raise ValueError(f"Logger '{name}' references unknown filter '{filter_name}'")
        
        # Set propagation
        logger.propagate = config.propagate
        
        return logger
    
    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of logger type names to classes"""
        return {'logger': logging.Logger}


class ComponentFactoryRegistry:
    """Registry for component factories"""
    
    def __init__(self):
        self.formatter_factory = FormatterFactory()
        self.filter_factory = FilterFactory()
        self.handler_factory = HandlerFactory(self.formatter_factory, self.filter_factory)
        self.logger_factory = LoggerFactory()
    
    def get_formatter_factory(self) -> FormatterFactory:
        """Get formatter factory"""
        return self.formatter_factory
    
    def get_filter_factory(self) -> FilterFactory:
        """Get filter factory"""
        return self.filter_factory
    
    def get_handler_factory(self) -> HandlerFactory:
        """Get handler factory"""
        return self.handler_factory
    
    def get_logger_factory(self) -> LoggerFactory:
        """Get logger factory"""
        return self.logger_factory
    
    def get_all_supported_types(self) -> Dict[str, Dict[str, Type]]:
        """Get all supported types from all factories"""
        return {
            'formatters': self.formatter_factory.get_supported_types(),
            'filters': self.filter_factory.get_supported_types(),
            'handlers': self.handler_factory.get_supported_types(),
            'loggers': self.logger_factory.get_supported_types(),
        }


# Global factory registry instance
_factory_registry: Optional[ComponentFactoryRegistry] = None


def get_factory_registry() -> ComponentFactoryRegistry:
    """Get the global factory registry instance"""
    global _factory_registry
    if _factory_registry is None:
        _factory_registry = ComponentFactoryRegistry()
    return _factory_registry

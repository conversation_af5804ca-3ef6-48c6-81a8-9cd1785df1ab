"""
Logging Configuration Module
Handles configuration loading, validation, and management for the logging system
"""
import os
import json
import yaml
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field, asdict
from pathlib import Path
import logging


@dataclass
class FormatterConfig:
    """Configuration for a formatter"""
    type: str
    include_context: bool = True
    use_unicode: Optional[bool] = None
    use_colors: Optional[bool] = None
    include_extra: bool = True


@dataclass
class FilterConfig:
    """Configuration for a filter"""
    type: str
    min_level: Optional[str] = None
    max_level: Optional[str] = None
    include_patterns: Optional[List[str]] = None
    exclude_patterns: Optional[List[str]] = None
    min_duration: Optional[float] = None
    max_duration: Optional[float] = None
    max_duplicates: int = 2
    time_window: float = 30.0


@dataclass
class HandlerConfig:
    """Configuration for a handler"""
    type: str
    level: str = "INFO"
    formatter: str = "default"
    filters: List[str] = field(default_factory=list)
    # File handler specific
    filename: Optional[str] = None
    max_bytes: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    when: str = "midnight"
    interval: int = 1
    # Database handler specific
    table_name: str = "system_logs"
    max_buffer_size: int = 100
    flush_interval: int = 30
    # Console handler specific
    stream: str = "stdout"


@dataclass
class LoggerConfig:
    """Configuration for a logger"""
    level: str = "INFO"
    handlers: List[str] = field(default_factory=list)
    propagate: bool = True
    filters: List[str] = field(default_factory=list)


@dataclass
class MonitoringConfig:
    """Configuration for monitoring"""
    enabled: bool = True
    interval: float = 60.0
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'cpu_percent': 80.0,
        'memory_percent': 85.0,
        'disk_usage_percent': 90.0,
        'avg_response_time': 5.0
    })
    max_history: int = 1440  # 24 hours at 1-minute intervals


@dataclass
class LoggingConfig:
    """Main logging configuration"""
    version: str = "1.0"
    level: str = "INFO"
    
    # Component configurations
    formatters: Dict[str, FormatterConfig] = field(default_factory=dict)
    filters: Dict[str, FilterConfig] = field(default_factory=dict)
    handlers: Dict[str, HandlerConfig] = field(default_factory=dict)
    loggers: Dict[str, LoggerConfig] = field(default_factory=dict)
    
    # Global settings
    root_handlers: List[str] = field(default_factory=lambda: ["console", "file"])
    disable_existing_loggers: bool = False
    
    # Monitoring configuration
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    
    # Third-party logger configurations
    third_party_loggers: Dict[str, str] = field(default_factory=lambda: {
        'uvicorn': 'INFO',
        'uvicorn.access': 'INFO',
        'uvicorn.error': 'INFO',
        'fastapi': 'INFO',
        'asyncpg': 'WARNING',
        'sqlalchemy': 'WARNING',
        'httpx': 'WARNING',
        'aiofiles': 'WARNING',
    })


class ConfigValidator:
    """Validates logging configuration"""
    
    VALID_LEVELS = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
    VALID_FORMATTER_TYPES = {'erp', 'json', 'colored', 'performance', 'database', 'security'}
    VALID_HANDLER_TYPES = {'console', 'file', 'rotating_file', 'timed_rotating_file', 'database'}
    VALID_FILTER_TYPES = {'level', 'module', 'performance', 'duplicate', 'rate_limit'}
    
    @classmethod
    def validate(cls, config: LoggingConfig) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        # Validate root level
        if config.level not in cls.VALID_LEVELS:
            errors.append(f"Invalid root level: {config.level}")
        
        # Validate formatters
        for name, formatter_config in config.formatters.items():
            if formatter_config.type not in cls.VALID_FORMATTER_TYPES:
                errors.append(f"Invalid formatter type '{formatter_config.type}' for formatter '{name}'")
        
        # Validate handlers
        for name, handler_config in config.handlers.items():
            if handler_config.type not in cls.VALID_HANDLER_TYPES:
                errors.append(f"Invalid handler type '{handler_config.type}' for handler '{name}'")
            
            if handler_config.level not in cls.VALID_LEVELS:
                errors.append(f"Invalid level '{handler_config.level}' for handler '{name}'")
            
            # Check if referenced formatter exists
            if handler_config.formatter not in config.formatters:
                errors.append(f"Handler '{name}' references unknown formatter '{handler_config.formatter}'")
            
            # Check if referenced filters exist
            for filter_name in handler_config.filters:
                if filter_name not in config.filters:
                    errors.append(f"Handler '{name}' references unknown filter '{filter_name}'")
        
        # Validate filters
        for name, filter_config in config.filters.items():
            if filter_config.type not in cls.VALID_FILTER_TYPES:
                errors.append(f"Invalid filter type '{filter_config.type}' for filter '{name}'")
            
            if filter_config.min_level and filter_config.min_level not in cls.VALID_LEVELS:
                errors.append(f"Invalid min_level '{filter_config.min_level}' for filter '{name}'")
            
            if filter_config.max_level and filter_config.max_level not in cls.VALID_LEVELS:
                errors.append(f"Invalid max_level '{filter_config.max_level}' for filter '{name}'")
        
        # Validate loggers
        for name, logger_config in config.loggers.items():
            if logger_config.level not in cls.VALID_LEVELS:
                errors.append(f"Invalid level '{logger_config.level}' for logger '{name}'")
            
            # Check if referenced handlers exist
            for handler_name in logger_config.handlers:
                if handler_name not in config.handlers:
                    errors.append(f"Logger '{name}' references unknown handler '{handler_name}'")
            
            # Check if referenced filters exist
            for filter_name in logger_config.filters:
                if filter_name not in config.filters:
                    errors.append(f"Logger '{name}' references unknown filter '{filter_name}'")
        
        # Validate root handlers
        for handler_name in config.root_handlers:
            if handler_name not in config.handlers:
                errors.append(f"Root configuration references unknown handler '{handler_name}'")
        
        return errors


class ConfigLoader:
    """Loads logging configuration from various sources"""
    
    @staticmethod
    def load_from_dict(config_dict: Dict[str, Any]) -> LoggingConfig:
        """Load configuration from dictionary"""
        return ConfigLoader._dict_to_config(config_dict)
    
    @staticmethod
    def load_from_file(config_path: Union[str, Path]) -> LoggingConfig:
        """Load configuration from file (JSON or YAML)"""
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.suffix.lower() in ['.yml', '.yaml']:
                config_dict = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                config_dict = json.load(f)
            else:
                raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")
        
        return ConfigLoader._dict_to_config(config_dict)
    
    @staticmethod
    def load_from_env() -> LoggingConfig:
        """Load configuration from environment variables"""
        config_dict = {}
        
        # Basic configuration from environment
        if 'LOG_LEVEL' in os.environ:
            config_dict['level'] = os.environ['LOG_LEVEL']
        
        if 'LOG_CONFIG_FILE' in os.environ:
            return ConfigLoader.load_from_file(os.environ['LOG_CONFIG_FILE'])
        
        return ConfigLoader._dict_to_config(config_dict)
    
    @staticmethod
    def get_default_config() -> LoggingConfig:
        """Get default logging configuration"""
        return LoggingConfig(
            formatters={
                'default': FormatterConfig(type='erp', include_context=True),
                'json': FormatterConfig(type='json', include_extra=True),
                'colored': FormatterConfig(type='colored', use_colors=True),
            },
            filters={
                'duplicate': FilterConfig(type='duplicate', max_duplicates=2, time_window=30.0),
            },
            handlers={
                'console': HandlerConfig(
                    type='console',
                    level='INFO',
                    formatter='colored',
                    filters=['duplicate']
                ),
                'file': HandlerConfig(
                    type='rotating_file',
                    level='DEBUG',
                    formatter='default',
                    filename='logs/erp.log',
                    max_bytes=10 * 1024 * 1024,
                    backup_count=5
                ),
            },
            loggers={
                'erp': LoggerConfig(level='DEBUG', handlers=['console', 'file']),
            }
        )
    
    @staticmethod
    def _dict_to_config(config_dict: Dict[str, Any]) -> LoggingConfig:
        """Convert dictionary to LoggingConfig object"""
        # Start with default config
        config = ConfigLoader.get_default_config()
        
        # Update with provided values
        if 'level' in config_dict:
            config.level = config_dict['level']
        
        if 'formatters' in config_dict:
            for name, formatter_dict in config_dict['formatters'].items():
                config.formatters[name] = FormatterConfig(**formatter_dict)
        
        if 'filters' in config_dict:
            for name, filter_dict in config_dict['filters'].items():
                config.filters[name] = FilterConfig(**filter_dict)
        
        if 'handlers' in config_dict:
            for name, handler_dict in config_dict['handlers'].items():
                config.handlers[name] = HandlerConfig(**handler_dict)
        
        if 'loggers' in config_dict:
            for name, logger_dict in config_dict['loggers'].items():
                config.loggers[name] = LoggerConfig(**logger_dict)
        
        if 'root_handlers' in config_dict:
            config.root_handlers = config_dict['root_handlers']
        
        if 'monitoring' in config_dict:
            config.monitoring = MonitoringConfig(**config_dict['monitoring'])
        
        if 'third_party_loggers' in config_dict:
            config.third_party_loggers.update(config_dict['third_party_loggers'])
        
        return config

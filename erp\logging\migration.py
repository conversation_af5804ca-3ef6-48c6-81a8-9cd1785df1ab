"""
Migration Guide and Compatibility Layer
Provides backward compatibility and migration utilities for the refactored logging system
"""
import warnings
from typing import Dict, Any, Optional
import logging

from .facade import get_logging_facade
from .config import LoggingConfig, ConfigLoader


class LegacyLoggingManager:
    """
    Compatibility wrapper for the old LoggingManager
    Provides the same interface but delegates to the new system
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        warnings.warn(
            "LoggingManager is deprecated. Use LoggingFacade or initialize_logging() instead.",
            DeprecationWarning,
            stacklevel=2
        )
        self.config = config or {}
        self.facade = get_logging_facade()
        self._initialized = False
    
    def initialize(self, config: Dict[str, Any]):
        """Initialize logging system with configuration"""
        if self._initialized:
            return
        
        # Convert old config format to new format if needed
        converted_config = self._convert_legacy_config(config)
        self.facade.initialize(converted_config)
        self._initialized = True
    
    def _convert_legacy_config(self, old_config: Dict[str, Any]) -> Dict[str, Any]:
        """Convert old configuration format to new format"""
        # This is a simplified conversion - in practice, you'd need to handle
        # all the specific configuration mappings from the old format
        new_config = {}
        
        # Map basic settings
        if 'log_level' in old_config:
            new_config['level'] = old_config['log_level']
        
        # Map console settings
        if old_config.get('log_console_enabled', True):
            new_config.setdefault('handlers', {})['console'] = {
                'type': 'console',
                'level': old_config.get('log_console_level', 'INFO'),
                'formatter': 'colored' if old_config.get('log_colored_console', True) else 'default'
            }
        
        # Map file settings
        if old_config.get('log_file_enabled', True):
            new_config.setdefault('handlers', {})['file'] = {
                'type': 'rotating_file' if old_config.get('log_file_rotating', True) else 'file',
                'level': old_config.get('log_file_level', 'DEBUG'),
                'filename': old_config.get('log_file', 'logs/erp.log'),
                'max_bytes': old_config.get('log_file_max_bytes', 10 * 1024 * 1024),
                'backup_count': old_config.get('log_file_backup_count', 5),
                'formatter': 'json' if old_config.get('log_file_json', False) else 'default'
            }
        
        return new_config
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger with the given name"""
        return self.facade.get_logger(name)
    
    def shutdown(self):
        """Shutdown logging system"""
        self.facade.shutdown()


def migrate_old_config_file(old_config_path: str, new_config_path: str):
    """
    Migrate an old configuration file to the new format
    
    Args:
        old_config_path: Path to the old configuration file
        new_config_path: Path where the new configuration should be saved
    """
    import json
    import yaml
    from pathlib import Path
    
    old_path = Path(old_config_path)
    new_path = Path(new_config_path)
    
    if not old_path.exists():
        raise FileNotFoundError(f"Old configuration file not found: {old_config_path}")
    
    # Load old configuration
    with open(old_path, 'r', encoding='utf-8') as f:
        if old_path.suffix.lower() in ['.yml', '.yaml']:
            old_config = yaml.safe_load(f)
        else:
            old_config = json.load(f)
    
    # Convert to new format
    manager = LegacyLoggingManager()
    new_config_dict = manager._convert_legacy_config(old_config)
    
    # Create new configuration object
    new_config = ConfigLoader.load_from_dict(new_config_dict)
    
    # Save new configuration
    with open(new_path, 'w', encoding='utf-8') as f:
        if new_path.suffix.lower() in ['.yml', '.yaml']:
            yaml.dump(new_config.__dict__, f, default_flow_style=False)
        else:
            json.dump(new_config.__dict__, f, indent=2, default=str)
    
    print(f"Configuration migrated from {old_config_path} to {new_config_path}")


def check_compatibility_issues(code_directory: str) -> Dict[str, Any]:
    """
    Scan code directory for potential compatibility issues
    
    Args:
        code_directory: Directory to scan for Python files
        
    Returns:
        Dictionary with compatibility issues found
    """
    import os
    import re
    from pathlib import Path
    
    issues = {
        'deprecated_imports': [],
        'deprecated_classes': [],
        'deprecated_functions': [],
        'potential_issues': []
    }
    
    # Patterns to look for
    deprecated_patterns = {
        'imports': [
            r'from\s+.*\.logging\s+import\s+.*LoggingManager',
            r'from\s+.*\.logging\.manager\s+import',
            r'from\s+.*\.logging\.monitoring\s+import',
            r'from\s+.*\.logging\.coordinator\s+import',
        ],
        'classes': [
            r'LoggingManager\s*\(',
            r'PerformanceMonitor\s*\(',
        ],
        'functions': [
            r'get_performance_monitor\s*\(',
            r'start_performance_monitoring\s*\(',
        ]
    }
    
    # Scan Python files
    for root, dirs, files in os.walk(code_directory):
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for deprecated patterns
                    for category, patterns in deprecated_patterns.items():
                        for pattern in patterns:
                            matches = re.findall(pattern, content)
                            if matches:
                                issues[f'deprecated_{category}'].append({
                                    'file': str(file_path),
                                    'pattern': pattern,
                                    'matches': matches
                                })
                
                except Exception as e:
                    issues['potential_issues'].append({
                        'file': str(file_path),
                        'error': str(e)
                    })
    
    return issues


def generate_migration_report(code_directory: str, output_file: str = 'migration_report.md'):
    """
    Generate a migration report for the codebase
    
    Args:
        code_directory: Directory to scan
        output_file: Output file for the report
    """
    issues = check_compatibility_issues(code_directory)
    
    report = """# Logging System Migration Report

## Overview
This report identifies potential compatibility issues when migrating to the refactored logging system.

## Deprecated Imports
The following imports are deprecated and should be updated:

"""
    
    for issue in issues['deprecated_imports']:
        report += f"- **File**: `{issue['file']}`\n"
        report += f"  **Pattern**: `{issue['pattern']}`\n"
        report += f"  **Matches**: {issue['matches']}\n\n"
    
    report += """
## Recommended Changes

### 1. Update Imports
Replace old imports with new facade imports:

```python
# Old
from erp.logging.manager import LoggingManager, get_logger
from erp.logging.monitoring import PerformanceMonitor

# New
from erp.logging import initialize_logging, get_logger
from erp.logging import start_performance_monitoring
```

### 2. Update Initialization
Replace LoggingManager with facade functions:

```python
# Old
manager = LoggingManager(config)
manager.initialize(config)

# New
initialize_logging(config)
```

### 3. Update Monitoring
Replace direct PerformanceMonitor usage:

```python
# Old
monitor = PerformanceMonitor()
monitor.start_monitoring()

# New
start_performance_monitoring()
```

## Migration Steps

1. **Backup your code**: Create a backup before making changes
2. **Update imports**: Replace deprecated imports with new facade imports
3. **Update initialization**: Replace LoggingManager with initialize_logging()
4. **Update monitoring**: Replace direct monitor usage with facade functions
5. **Test thoroughly**: Ensure all logging functionality works as expected
6. **Remove deprecated code**: Clean up any remaining deprecated usage

## Benefits of Migration

- **Simplified Interface**: Easier to use facade pattern
- **Better Separation of Concerns**: Modular architecture
- **Improved Performance**: Optimized components
- **Enhanced Monitoring**: Better metrics and alerting
- **Future-Proof**: Extensible design for new features

"""
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"Migration report generated: {output_file}")


# Compatibility functions for smooth migration
def get_logging_manager():
    """
    Deprecated: Get logging manager
    Use get_logging_facade() instead
    """
    warnings.warn(
        "get_logging_manager() is deprecated. Use get_logging_facade() instead.",
        DeprecationWarning,
        stacklevel=2
    )
    return LegacyLoggingManager()


def get_performance_monitor():
    """
    Deprecated: Get performance monitor
    Use start_performance_monitoring() instead
    """
    warnings.warn(
        "get_performance_monitor() is deprecated. Use start_performance_monitoring() instead.",
        DeprecationWarning,
        stacklevel=2
    )
    facade = get_logging_facade()
    return facade.monitor


# Export compatibility functions
__all__ = [
    'LegacyLoggingManager',
    'migrate_old_config_file',
    'check_compatibility_issues',
    'generate_migration_report',
    'get_logging_manager',
    'get_performance_monitor',
]

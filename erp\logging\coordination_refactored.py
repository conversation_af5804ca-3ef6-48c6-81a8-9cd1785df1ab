"""
Refactored Coordination Module
Focused coordination components with single responsibilities
"""
import logging
import threading
from typing import Set, Dict, Any, Optional, List
from contextlib import contextmanager
from datetime import datetime, timedelta
from abc import ABC, abstractmethod


class OperationTracker:
    """Tracks active operations to prevent duplicates"""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._active_operations: Set[str] = set()
        self._operation_contexts: Dict[str, Dict[str, Any]] = {}
        self._operation_history: List[Dict[str, Any]] = []
        self._max_history = 1000
    
    def start_operation(self, operation_id: str, **context) -> bool:
        """
        Start tracking an operation
        
        Returns:
            True if operation started successfully, False if already active
        """
        with self._lock:
            if operation_id in self._active_operations:
                return False
            
            self._active_operations.add(operation_id)
            self._operation_contexts[operation_id] = {
                'start_time': datetime.now(),
                **context
            }
            return True
    
    def end_operation(self, operation_id: str, **result_context):
        """End tracking an operation"""
        with self._lock:
            if operation_id not in self._active_operations:
                return
            
            # Move to history
            operation_data = self._operation_contexts.pop(operation_id, {})
            operation_data.update({
                'operation_id': operation_id,
                'end_time': datetime.now(),
                'duration': (datetime.now() - operation_data.get('start_time', datetime.now())).total_seconds(),
                **result_context
            })
            
            self._operation_history.append(operation_data)
            if len(self._operation_history) > self._max_history:
                self._operation_history = self._operation_history[-self._max_history:]
            
            self._active_operations.discard(operation_id)
    
    def is_operation_active(self, operation_id: str) -> bool:
        """Check if an operation is currently active"""
        with self._lock:
            return operation_id in self._active_operations
    
    def get_active_operations(self) -> Dict[str, Dict[str, Any]]:
        """Get currently active operations and their contexts"""
        with self._lock:
            return self._operation_contexts.copy()
    
    def get_operation_history(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get operation history from the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        with self._lock:
            return [op for op in self._operation_history 
                   if op.get('end_time', datetime.now()) >= cutoff_time]
    
    @contextmanager
    def operation_context(self, operation_id: str, **context):
        """Context manager for tracking operations"""
        started = self.start_operation(operation_id, **context)
        try:
            yield started
        finally:
            if started:
                self.end_operation(operation_id)


class LoggerSuppressor:
    """Manages temporary suppression of loggers"""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._suppressed_loggers: Dict[str, int] = {}  # logger_name -> original_level
        self._suppression_counts: Dict[str, int] = {}  # logger_name -> suppression_count
    
    def suppress_logger(self, logger_name: str, level: int = logging.WARNING):
        """Temporarily suppress a logger to reduce verbosity"""
        with self._lock:
            logger = logging.getLogger(logger_name)
            
            if logger_name not in self._suppressed_loggers:
                # Store original level
                self._suppressed_loggers[logger_name] = logger.level
                self._suppression_counts[logger_name] = 0
            
            # Increase suppression count
            self._suppression_counts[logger_name] += 1
            
            # Set suppression level
            logger.setLevel(level)
    
    def restore_logger(self, logger_name: str):
        """Restore a logger's normal logging level"""
        with self._lock:
            if logger_name not in self._suppressed_loggers:
                return
            
            # Decrease suppression count
            self._suppression_counts[logger_name] -= 1
            
            # Only restore if no more suppressions
            if self._suppression_counts[logger_name] <= 0:
                logger = logging.getLogger(logger_name)
                original_level = self._suppressed_loggers.pop(logger_name)
                self._suppression_counts.pop(logger_name, None)
                logger.setLevel(original_level)
    
    def is_suppressed(self, logger_name: str) -> bool:
        """Check if a logger is currently suppressed"""
        with self._lock:
            return logger_name in self._suppressed_loggers
    
    def get_suppressed_loggers(self) -> List[str]:
        """Get list of currently suppressed loggers"""
        with self._lock:
            return list(self._suppressed_loggers.keys())
    
    @contextmanager
    def quiet_operation(self, *logger_names, level: int = logging.WARNING):
        """Context manager to temporarily reduce logging verbosity"""
        for logger_name in logger_names:
            self.suppress_logger(logger_name, level)
        
        try:
            yield
        finally:
            for logger_name in logger_names:
                self.restore_logger(logger_name)


class LoggingRateLimiter:
    """Rate limits logging to prevent spam"""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._message_counts: Dict[str, List[datetime]] = {}
        self._rate_limits: Dict[str, Dict[str, Any]] = {}
    
    def set_rate_limit(self, logger_name: str, max_messages: int, time_window: int):
        """Set rate limit for a logger"""
        with self._lock:
            self._rate_limits[logger_name] = {
                'max_messages': max_messages,
                'time_window': time_window
            }
    
    def should_log(self, logger_name: str, message: str) -> bool:
        """Check if a message should be logged based on rate limits"""
        with self._lock:
            if logger_name not in self._rate_limits:
                return True
            
            rate_limit = self._rate_limits[logger_name]
            message_key = f"{logger_name}:{hash(message)}"
            
            now = datetime.now()
            cutoff_time = now - timedelta(seconds=rate_limit['time_window'])
            
            # Clean old messages
            if message_key in self._message_counts:
                self._message_counts[message_key] = [
                    timestamp for timestamp in self._message_counts[message_key]
                    if timestamp > cutoff_time
                ]
            else:
                self._message_counts[message_key] = []
            
            # Check rate limit
            if len(self._message_counts[message_key]) >= rate_limit['max_messages']:
                return False
            
            # Record this message
            self._message_counts[message_key].append(now)
            return True
    
    def get_rate_limit_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get rate limiting statistics"""
        with self._lock:
            stats = {}
            for logger_name, rate_limit in self._rate_limits.items():
                message_counts = [
                    len(timestamps) for key, timestamps in self._message_counts.items()
                    if key.startswith(f"{logger_name}:")
                ]
                
                stats[logger_name] = {
                    'rate_limit': rate_limit,
                    'active_messages': len(message_counts),
                    'total_recent_messages': sum(message_counts)
                }
            
            return stats


class LoggingCoordinator:
    """Coordinates logging operations with focused responsibilities"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.operation_tracker = OperationTracker()
        self.logger_suppressor = LoggerSuppressor()
        self.rate_limiter = LoggingRateLimiter()
    
    def start_operation(self, operation_id: str, **context) -> bool:
        """Start tracking an operation"""
        return self.operation_tracker.start_operation(operation_id, **context)
    
    def end_operation(self, operation_id: str, **result_context):
        """End tracking an operation"""
        self.operation_tracker.end_operation(operation_id, **result_context)
    
    def is_operation_active(self, operation_id: str) -> bool:
        """Check if an operation is currently active"""
        return self.operation_tracker.is_operation_active(operation_id)
    
    def suppress_logger(self, logger_name: str, level: int = logging.WARNING):
        """Temporarily suppress a logger"""
        self.logger_suppressor.suppress_logger(logger_name, level)
    
    def restore_logger(self, logger_name: str):
        """Restore a logger's normal level"""
        self.logger_suppressor.restore_logger(logger_name)
    
    def set_rate_limit(self, logger_name: str, max_messages: int, time_window: int):
        """Set rate limit for a logger"""
        self.rate_limiter.set_rate_limit(logger_name, max_messages, time_window)
    
    def should_log(self, logger_name: str, message: str) -> bool:
        """Check if a message should be logged"""
        return self.rate_limiter.should_log(logger_name, message)
    
    @contextmanager
    def operation_context(self, operation_id: str, **context):
        """Context manager for operation tracking"""
        with self.operation_tracker.operation_context(operation_id, **context) as started:
            yield started
    
    @contextmanager
    def quiet_operation(self, *logger_names, level: int = logging.WARNING):
        """Context manager for quiet operations"""
        with self.logger_suppressor.quiet_operation(*logger_names, level=level):
            yield
    
    def get_coordination_stats(self) -> Dict[str, Any]:
        """Get coordination statistics"""
        return {
            'active_operations': len(self.operation_tracker.get_active_operations()),
            'suppressed_loggers': len(self.logger_suppressor.get_suppressed_loggers()),
            'rate_limited_loggers': len(self.rate_limiter._rate_limits),
            'operation_history_count': len(self.operation_tracker._operation_history),
        }
    
    def get_detailed_stats(self) -> Dict[str, Any]:
        """Get detailed coordination statistics"""
        return {
            'active_operations': self.operation_tracker.get_active_operations(),
            'suppressed_loggers': self.logger_suppressor.get_suppressed_loggers(),
            'rate_limit_stats': self.rate_limiter.get_rate_limit_stats(),
            'recent_operations': self.operation_tracker.get_operation_history(60),
        }


# Global coordinator instance
_coordinator: Optional[LoggingCoordinator] = None


def get_logging_coordinator() -> LoggingCoordinator:
    """Get the global logging coordinator instance"""
    global _coordinator
    if _coordinator is None:
        _coordinator = LoggingCoordinator()
    return _coordinator


def operation_context(operation_id: str, **context):
    """Convenience function for operation context"""
    return get_logging_coordinator().operation_context(operation_id, **context)


def quiet_operation(*logger_names, level: int = logging.WARNING):
    """Convenience function for quiet operation"""
    return get_logging_coordinator().quiet_operation(*logger_names, level=level)


def is_operation_active(operation_id: str) -> bool:
    """Convenience function to check if operation is active"""
    return get_logging_coordinator().is_operation_active(operation_id)


def set_rate_limit(logger_name: str, max_messages: int, time_window: int):
    """Convenience function to set rate limit"""
    get_logging_coordinator().set_rate_limit(logger_name, max_messages, time_window)

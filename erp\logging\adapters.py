"""
Logging Adapters Module
Adapter pattern implementation for different logging backends and integrations
"""
import logging
import json
import asyncio
import threading
from typing import Dict, Any, Optional, List, Protocol
from abc import ABC, abstractmethod
from datetime import datetime
import queue
import time


class LoggingBackend(Protocol):
    """Protocol for logging backends"""
    
    def write_log(self, record: Dict[str, Any]) -> None:
        """Write a log record to the backend"""
        ...
    
    def flush(self) -> None:
        """Flush any buffered logs"""
        ...
    
    def close(self) -> None:
        """Close the backend connection"""
        ...


class LoggingAdapter(ABC):
    """Abstract base class for logging adapters"""
    
    def __init__(self, backend: LoggingBackend, buffer_size: int = 100, flush_interval: float = 30.0):
        self.backend = backend
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        self.buffer: List[Dict[str, Any]] = []
        self.buffer_lock = threading.Lock()
        self.last_flush = time.time()
        self.closed = False
    
    @abstractmethod
    def format_record(self, record: logging.LogRecord) -> Dict[str, Any]:
        """Format a log record for the backend"""
        pass
    
    def emit(self, record: logging.LogRecord):
        """Emit a log record to the backend"""
        if self.closed:
            return
        
        try:
            formatted_record = self.format_record(record)
            
            with self.buffer_lock:
                self.buffer.append(formatted_record)
                
                # Check if we need to flush
                should_flush = (
                    len(self.buffer) >= self.buffer_size or
                    time.time() - self.last_flush >= self.flush_interval
                )
                
                if should_flush:
                    self._flush_buffer()
        
        except Exception as e:
            # Don't let adapter errors break logging
            pass
    
    def _flush_buffer(self):
        """Flush the buffer to the backend"""
        if not self.buffer:
            return
        
        records_to_flush = self.buffer.copy()
        self.buffer.clear()
        self.last_flush = time.time()
        
        try:
            for record in records_to_flush:
                self.backend.write_log(record)
            self.backend.flush()
        except Exception as e:
            # Re-add records to buffer if flush failed
            self.buffer.extend(records_to_flush)
    
    def flush(self):
        """Manually flush the buffer"""
        with self.buffer_lock:
            self._flush_buffer()
    
    def close(self):
        """Close the adapter and backend"""
        if not self.closed:
            self.flush()
            self.backend.close()
            self.closed = True


class ConsoleBackend:
    """Backend for console output"""
    
    def __init__(self, stream=None):
        import sys
        self.stream = stream or sys.stdout
    
    def write_log(self, record: Dict[str, Any]) -> None:
        """Write log record to console"""
        message = record.get('formatted_message', str(record))
        self.stream.write(message + '\n')
    
    def flush(self) -> None:
        """Flush console stream"""
        self.stream.flush()
    
    def close(self) -> None:
        """Close console stream"""
        pass  # Don't close stdout/stderr


class FileBackend:
    """Backend for file output"""
    
    def __init__(self, filename: str, encoding: str = 'utf-8'):
        self.filename = filename
        self.encoding = encoding
        self.file = None
        self._open_file()
    
    def _open_file(self):
        """Open the log file"""
        try:
            self.file = open(self.filename, 'a', encoding=self.encoding)
        except Exception as e:
            raise RuntimeError(f"Failed to open log file {self.filename}: {e}")
    
    def write_log(self, record: Dict[str, Any]) -> None:
        """Write log record to file"""
        if not self.file:
            self._open_file()
        
        if 'formatted_message' in record:
            message = record['formatted_message']
        else:
            message = json.dumps(record, default=str)
        
        self.file.write(message + '\n')
    
    def flush(self) -> None:
        """Flush file buffer"""
        if self.file:
            self.file.flush()
    
    def close(self) -> None:
        """Close file"""
        if self.file:
            self.file.close()
            self.file = None


class DatabaseBackend:
    """Backend for database output"""
    
    def __init__(self, table_name: str = 'system_logs', connection_factory=None):
        self.table_name = table_name
        self.connection_factory = connection_factory
        self.connection = None
        self._setup_table()
    
    def _setup_table(self):
        """Setup database table for logs"""
        # This would be implemented based on the specific database
        # For now, we'll just store the schema
        self.schema = {
            'id': 'SERIAL PRIMARY KEY',
            'timestamp': 'TIMESTAMP',
            'level': 'VARCHAR(20)',
            'logger': 'VARCHAR(255)',
            'message': 'TEXT',
            'module': 'VARCHAR(255)',
            'function': 'VARCHAR(255)',
            'line': 'INTEGER',
            'user_id': 'VARCHAR(100)',
            'database': 'VARCHAR(100)',
            'request_id': 'VARCHAR(100)',
            'extra_data': 'JSONB'
        }
    
    def _get_connection(self):
        """Get database connection"""
        if not self.connection and self.connection_factory:
            self.connection = self.connection_factory()
        return self.connection
    
    def write_log(self, record: Dict[str, Any]) -> None:
        """Write log record to database"""
        conn = self._get_connection()
        if not conn:
            return
        
        try:
            # Extract standard fields
            fields = {
                'timestamp': record.get('timestamp'),
                'level': record.get('level'),
                'logger': record.get('logger'),
                'message': record.get('message'),
                'module': record.get('module'),
                'function': record.get('function'),
                'line': record.get('line'),
                'user_id': record.get('user_id'),
                'database': record.get('database'),
                'request_id': record.get('request_id'),
            }
            
            # Store extra fields as JSON
            extra_data = {k: v for k, v in record.items() if k not in fields}
            fields['extra_data'] = json.dumps(extra_data, default=str)
            
            # Build and execute insert query
            columns = ', '.join(fields.keys())
            placeholders = ', '.join(['%s'] * len(fields))
            query = f"INSERT INTO {self.table_name} ({columns}) VALUES ({placeholders})"
            
            cursor = conn.cursor()
            cursor.execute(query, list(fields.values()))
            cursor.close()
            
        except Exception as e:
            # Log database errors to console as fallback
            print(f"Database logging error: {e}")
    
    def flush(self) -> None:
        """Flush database connection"""
        if self.connection:
            try:
                self.connection.commit()
            except Exception:
                pass
    
    def close(self) -> None:
        """Close database connection"""
        if self.connection:
            try:
                self.connection.close()
            except Exception:
                pass
            self.connection = None


class RemoteBackend:
    """Backend for remote logging services"""
    
    def __init__(self, endpoint: str, api_key: Optional[str] = None, timeout: float = 5.0):
        self.endpoint = endpoint
        self.api_key = api_key
        self.timeout = timeout
        self.session = None
    
    def _get_session(self):
        """Get HTTP session"""
        if not self.session:
            import requests
            self.session = requests.Session()
            if self.api_key:
                self.session.headers.update({'Authorization': f'Bearer {self.api_key}'})
        return self.session
    
    def write_log(self, record: Dict[str, Any]) -> None:
        """Write log record to remote service"""
        try:
            session = self._get_session()
            response = session.post(
                self.endpoint,
                json=record,
                timeout=self.timeout
            )
            response.raise_for_status()
        except Exception as e:
            # Log remote errors to console as fallback
            print(f"Remote logging error: {e}")
    
    def flush(self) -> None:
        """Flush remote connection"""
        pass  # HTTP requests are immediate
    
    def close(self) -> None:
        """Close remote connection"""
        if self.session:
            self.session.close()
            self.session = None


class ConsoleAdapter(LoggingAdapter):
    """Adapter for console logging"""
    
    def __init__(self, stream=None, **kwargs):
        backend = ConsoleBackend(stream)
        super().__init__(backend, **kwargs)
    
    def format_record(self, record: logging.LogRecord) -> Dict[str, Any]:
        """Format record for console output"""
        # Use the record's formatter if available
        if hasattr(record, 'formatter') and record.formatter:
            formatted_message = record.formatter.format(record)
        else:
            formatted_message = record.getMessage()
        
        return {
            'formatted_message': formatted_message,
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
        }


class FileAdapter(LoggingAdapter):
    """Adapter for file logging"""
    
    def __init__(self, filename: str, encoding: str = 'utf-8', **kwargs):
        backend = FileBackend(filename, encoding)
        super().__init__(backend, **kwargs)
    
    def format_record(self, record: logging.LogRecord) -> Dict[str, Any]:
        """Format record for file output"""
        # Use the record's formatter if available
        if hasattr(record, 'formatter') and record.formatter:
            formatted_message = record.formatter.format(record)
        else:
            formatted_message = record.getMessage()
        
        return {
            'formatted_message': formatted_message,
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
        }


class DatabaseAdapter(LoggingAdapter):
    """Adapter for database logging"""
    
    def __init__(self, table_name: str = 'system_logs', connection_factory=None, **kwargs):
        backend = DatabaseBackend(table_name, connection_factory)
        super().__init__(backend, **kwargs)
    
    def format_record(self, record: logging.LogRecord) -> Dict[str, Any]:
        """Format record for database storage"""
        formatted_record = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process,
        }
        
        # Add ERP-specific fields
        erp_fields = ['user_id', 'database', 'request_id', 'session_id', 'addon_name', 'model_name']
        for field in erp_fields:
            if hasattr(record, field):
                formatted_record[field] = getattr(record, field)
        
        # Add any extra attributes
        for key, value in record.__dict__.items():
            if key not in formatted_record and not key.startswith('_'):
                formatted_record[key] = value
        
        return formatted_record


class RemoteAdapter(LoggingAdapter):
    """Adapter for remote logging"""
    
    def __init__(self, endpoint: str, api_key: Optional[str] = None, timeout: float = 5.0, **kwargs):
        backend = RemoteBackend(endpoint, api_key, timeout)
        super().__init__(backend, **kwargs)
    
    def format_record(self, record: logging.LogRecord) -> Dict[str, Any]:
        """Format record for remote service"""
        return {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'user_id': getattr(record, 'user_id', None),
            'database': getattr(record, 'database', None),
            'request_id': getattr(record, 'request_id', None),
            'extra': {k: v for k, v in record.__dict__.items() 
                     if not k.startswith('_') and k not in 
                     ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                      'filename', 'module', 'lineno', 'funcName', 'created', 
                      'msecs', 'relativeCreated', 'thread', 'threadName', 'processName', 'process']}
        }

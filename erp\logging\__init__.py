"""
ERP Logging System - Refactored Modular Architecture
Comprehensive logging module with focused, single-responsibility components
"""

# Main facade interface (recommended for most use cases)
from .facade import (
    LoggingFacade,
    get_logging_facade,
    initialize_logging,
    get_logger,
    start_performance_monitoring,
    stop_performance_monitoring,
    record_request,
    operation_context,
    quiet_operation,
    get_system_status,
    shutdown_logging,
    test_logging_colors,
    log_performance,
    log_structured,
    LogContext,
    log_method_calls,
    log_database_operations,
    log_api_calls,
    log_security_events,
)

# Configuration system
from .config import (
    LoggingConfig,
    FormatterConfig,
    HandlerConfig,
    FilterConfig,
    LoggerConfig,
    MonitoringConfig,
    ConfigLoader,
    ConfigValidator,
)

# Core system components (for advanced usage)
from .core import (
    LoggingSystem,
    ComponentRegistry,
    LoggerManager,
    get_logging_system,
)

# Factory system (for custom component creation)
from .factories import (
    ComponentFactoryRegistry,
    FormatterFactory,
    FilterFactory,
    HandlerFactory,
    LoggerFactory,
    get_factory_registry,
)

# Middleware system (for custom processing pipelines)
from .middleware import (
    LoggingPipeline,
    LoggingMiddleware,
    ContextMiddleware,
    PerformanceMiddleware,
    SecurityMiddleware,
    FilterMiddleware,
    get_global_pipeline,
    process_log_record,
)

# Monitoring and coordination (for advanced monitoring)
from .monitoring_refactored import (
    PerformanceMonitor,
    SystemMetrics,
    ApplicationMetrics,
    MetricsCollector,
    AlertManager,
    MetricsStorage,
)

from .coordination_refactored import (
    LoggingCoordinator,
    OperationTracker,
    LoggerSuppressor,
    LoggingRateLimiter,
    get_logging_coordinator,
)

# Adapters (for custom backends)
from .adapters import (
    LoggingAdapter,
    ConsoleAdapter,
    FileAdapter,
    DatabaseAdapter,
    RemoteAdapter,
)

# Legacy components (for backward compatibility)
from .formatters import ERPFormatter, JSONFormatter, ColoredFormatter
from .handlers import RotatingFileHandler, TimedRotatingFileHandler, DatabaseHandler
from .filters import LevelFilter, ModuleFilter, PerformanceFilter, DuplicateFilter

__all__ = [
    # Main facade interface (recommended)
    'LoggingFacade',
    'get_logging_facade',
    'initialize_logging',
    'get_logger',
    'start_performance_monitoring',
    'stop_performance_monitoring',
    'record_request',
    'operation_context',
    'quiet_operation',
    'get_system_status',
    'shutdown_logging',
    'test_logging_colors',

    # Utility functions and decorators
    'log_performance',
    'log_structured',
    'LogContext',
    'log_method_calls',
    'log_database_operations',
    'log_api_calls',
    'log_security_events',

    # Configuration system
    'LoggingConfig',
    'FormatterConfig',
    'HandlerConfig',
    'FilterConfig',
    'LoggerConfig',
    'MonitoringConfig',
    'ConfigLoader',
    'ConfigValidator',

    # Core system (advanced usage)
    'LoggingSystem',
    'ComponentRegistry',
    'LoggerManager',
    'get_logging_system',

    # Factory system (custom components)
    'ComponentFactoryRegistry',
    'FormatterFactory',
    'FilterFactory',
    'HandlerFactory',
    'LoggerFactory',
    'get_factory_registry',

    # Middleware system (custom processing)
    'LoggingPipeline',
    'LoggingMiddleware',
    'ContextMiddleware',
    'PerformanceMiddleware',
    'SecurityMiddleware',
    'FilterMiddleware',
    'get_global_pipeline',
    'process_log_record',

    # Monitoring and coordination (advanced monitoring)
    'PerformanceMonitor',
    'SystemMetrics',
    'ApplicationMetrics',
    'MetricsCollector',
    'AlertManager',
    'MetricsStorage',
    'LoggingCoordinator',
    'OperationTracker',
    'LoggerSuppressor',
    'LoggingRateLimiter',
    'get_logging_coordinator',

    # Adapters (custom backends)
    'LoggingAdapter',
    'ConsoleAdapter',
    'FileAdapter',
    'DatabaseAdapter',
    'RemoteAdapter',

    # Legacy components (backward compatibility)
    'ERPFormatter',
    'JSONFormatter',
    'ColoredFormatter',
    'RotatingFileHandler',
    'TimedRotatingFileHandler',
    'DatabaseHandler',
    'LevelFilter',
    'ModuleFilter',
    'PerformanceFilter',
    'DuplicateFilter',
]
